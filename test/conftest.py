"""
Pytest configuration and fixtures for score.py tests.
"""

import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

# Add the Python directory to the path so we can import score
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Python'))


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def mock_model():
    """Create a mock Keras model."""
    model = Mock()
    model.predict.return_value = [[0.1, 0.2, 0.7], [0.6, 0.3, 0.1]]
    return model


@pytest.fixture
def mock_tokenizer():
    """Create a mock tokenizer."""
    tokenizer = Mock()
    tokenizer.texts_to_matrix.return_value = [[1, 0, 1], [0, 1, 0]]
    return tokenizer


@pytest.fixture
def mock_label_encoder():
    """Create a mock label encoder."""
    encoder = Mock()
    encoder.inverse_transform.return_value = ['label1', 'label2']
    return encoder


@pytest.fixture
def mock_spell_checker():
    """Create a mock SymSpell spell checker."""
    spell_checker = Mock()
    suggestion = Mock()
    suggestion.distance = 1
    suggestion.term = 'corrected'
    spell_checker.lookup.return_value = [suggestion]
    return spell_checker


@pytest.fixture
def sample_abbreviation_data():
    """Sample abbreviation data for testing."""
    return {
        'Abbreviation': ['ABC', 'DEF', 'CS_ABBR'],
        'FullText': ['full_abc', 'full_def', 'case_sensitive_full'],
        'Comment': [None, None, 'CS'],
    }


@pytest.fixture
def sample_input_data():
    """Sample input data for testing."""
    return [
        {'Pair_ID': 1001, 'T': 'Discount to Invoice', 'AmountExVat': 80},
        {'Pair_ID': 1002, 'T': 'Service Fee $9.9900 S', 'AmountExVat': 9.99},
        {'Pair_ID': 1003, 'T': '', 'AmountExVat': 0},  # Empty treatment for T202 rule testing
    ]


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state before each test."""
    # Import score module to reset its global state
    import sys
    from pathlib import Path

    sys.path.insert(0, str(Path(__file__).parent.parent))
    from autolodge import score

    score._resource_manager = None
    yield
    # Clean up after test
    score._resource_manager = None


# Azure Blob Storage Testing Fixtures


@pytest.fixture
def mock_blob_service_client():
    """Create a mock Azure BlobServiceClient."""
    with patch('autolodge.log_utils.azure_sink.BlobServiceClient') as mock_client_class:
        mock_client = MagicMock()
        mock_client_class.from_connection_string.return_value = mock_client

        # Mock container operations
        mock_client.create_container.return_value = None

        # Mock blob client
        mock_blob_client = MagicMock()
        mock_client.get_blob_client.return_value = mock_blob_client

        # Mock blob operations
        mock_blob_client.download_blob.return_value.readall.return_value = b'existing log content\n'
        mock_blob_client.upload_blob.return_value = None

        yield mock_client


@pytest.fixture
def azure_config_env_vars():
    """Set up environment variables for Azure configuration testing."""
    env_vars = {
        'AZURE_STORAGE_CONNECTION_STRING': 'DefaultEndpointsProtocol=https;AccountName=testaccount;AccountKey=testkey;EndpointSuffix=core.windows.net',
        'AZURE_LOG_CONTAINER_NAME': 'test-logs',
        'AZURE_LOG_BLOB_PREFIX': 'test-score',
        'DEPLOYMENT_ENVIRONMENT': 'TEST',
    }

    with patch.dict(os.environ, env_vars, clear=False):
        yield env_vars


@pytest.fixture
def azure_config_minimal_env():
    """Set up minimal environment variables for Azure configuration testing."""
    env_vars = {'AZURE_STORAGE_ACCOUNT_NAME': 'testaccount', 'AZURE_STORAGE_ACCOUNT_KEY': 'testkey123456789'}

    with patch.dict(os.environ, env_vars, clear=False):
        yield env_vars


@pytest.fixture
def mock_loguru_message():
    """Create a mock Loguru message record."""
    message = MagicMock()
    message.record = {
        'time': MagicMock(),
        'level': MagicMock(),
        'name': 'test_module',
        'function': 'test_function',
        'line': 42,
        'message': 'Test log message',
    }

    # Mock the format method
    message.record['time'].strftime = MagicMock(return_value='2024-01-15 10:30:45')
    message.record['level'].__str__ = MagicMock(return_value='INFO')

    return message


@pytest.fixture
def clean_environment():
    """Clean environment variables for testing."""
    azure_env_vars = [
        'AZURE_STORAGE_CONNECTION_STRING',
        'AZURE_STORAGE_ACCOUNT_NAME',
        'AZURE_STORAGE_ACCOUNT_KEY',
        'AZURE_LOG_CONTAINER_NAME',
        'AZURE_LOG_BLOB_PREFIX',
        'DEPLOYMENT_ENVIRONMENT',
        'APP_ENVIRONMENT',
        'AZURE_ML_WORKSPACE_NAME',
        'MODEL_NAME',
    ]

    # Store original values
    original_values = {}
    for var in azure_env_vars:
        original_values[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]

    yield

    # Restore original values
    for var, value in original_values.items():
        if value is not None:
            os.environ[var] = value
        elif var in os.environ:
            del os.environ[var]
