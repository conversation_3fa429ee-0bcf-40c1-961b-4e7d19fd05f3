"""
Integration tests for the complete Azure Blob Storage logging setup.

This module tests the integration between AzureLoggingConfig, AzureBlobStorageSink,
and the setup_logging function to ensure the complete logging workflow functions correctly.
"""

import os
import sys
from unittest.mock import MagicMock, Mock, patch, call
from pathlib import Path

import pytest

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from autolodge.log_utils import setup_logging, AzureLoggingConfig, AzureBlobStorageSink
from autolodge.log_utils.config import check_azure_dependencies


class TestLoggingSetupIntegration:
    """Integration tests for the complete logging setup workflow."""

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_azure_success(self, mock_logger, azure_config_env_vars, mock_blob_service_client):
        """Test successful setup of Azure Blob Storage logging."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=True), \
             patch('autolodge.log_utils.utils.AzureBlobStorageSink') as mock_sink_class, \
             patch('threading.Thread'):

            mock_sink = Mock()
            mock_sink.current_blob_name = 'test_TEST_2024-01-15_10-30-45.log'
            mock_sink_class.return_value = mock_sink

            result = setup_logging()

            # Verify Azure sink was created with correct parameters
            mock_sink_class.assert_called_once()
            call_args = mock_sink_class.call_args
            assert 'connection_string' in call_args.kwargs
            assert call_args.kwargs['container_name'] == 'test-logs'
            assert call_args.kwargs['blob_prefix'] == 'test-score'
            assert call_args.kwargs['environment'] == 'TEST'

            # Verify return value
            assert 'azure://test-logs/test_TEST_2024-01-15_10-30-45.log' in result

            # Verify success was logged
            mock_logger.info.assert_any_call('✅ Azure Blob Storage logging configured successfully')

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_azure_failure_fallback_to_local(self, mock_logger, azure_config_env_vars):
        """Test fallback to local file logging when Azure setup fails."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=True), \
             patch('autolodge.log_utils.utils.AzureBlobStorageSink', side_effect=Exception('Azure connection failed')), \
             patch('autolodge.log_utils.utils._setup_local_file_logging', return_value='/logs/local_file.log') as mock_local_setup:

            result = setup_logging()

            # Verify fallback to local logging
            mock_local_setup.assert_called_once()
            assert result == '/logs/local_file.log'

            # Verify error and warning were logged
            mock_logger.error.assert_any_call('❌ Failed to configure Azure Blob Storage logging: Azure connection failed')
            mock_logger.warning.assert_any_call('⚠️ Falling back to local file logging')

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_no_azure_dependencies(self, mock_logger, azure_config_env_vars):
        """Test setup when Azure dependencies are not available."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=False), \
             patch('autolodge.log_utils.utils._setup_local_file_logging', return_value='/logs/local_file.log') as mock_local_setup:

            result = setup_logging()

            # Verify fallback to local logging
            mock_local_setup.assert_called_once()
            assert result == '/logs/local_file.log'

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_no_azure_configuration(self, mock_logger, clean_environment):
        """Test setup when Azure is not configured."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=True), \
             patch('autolodge.log_utils.utils._setup_local_file_logging', return_value='/logs/local_file.log') as mock_local_setup:

            result = setup_logging()

            # Verify fallback to local logging
            mock_local_setup.assert_called_once()
            assert result == '/logs/local_file.log'

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_logger_configuration(self, mock_logger, azure_config_env_vars, mock_blob_service_client):
        """Test that logger is properly configured during setup."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=True), \
             patch('autolodge.log_utils.utils.AzureBlobStorageSink') as mock_sink_class, \
             patch('threading.Thread'), \
             patch('autolodge.log_utils.utils.logger.remove') as mock_remove, \
             patch('autolodge.log_utils.utils.logger.add') as mock_add:

            mock_sink = Mock()
            mock_sink.current_blob_name = 'test_TEST_2024-01-15_10-30-45.log'
            mock_sink_class.return_value = mock_sink

            setup_logging()

            # Verify logger was configured
            mock_remove.assert_called_once()
            assert mock_add.call_count >= 2  # Console + Azure handlers

            # Verify Azure handler was added with correct parameters
            azure_handler_call = None
            for call in mock_add.call_args_list:
                if 'sink' in call.kwargs and hasattr(call.kwargs['sink'], '__name__'):
                    continue  # Skip console handler
                azure_handler_call = call
                break

            assert azure_handler_call is not None
            assert azure_handler_call.kwargs['level'] == 'DEBUG'
            assert azure_handler_call.kwargs['enqueue'] is True
            assert azure_handler_call.kwargs['backtrace'] is True
            assert azure_handler_call.kwargs['diagnose'] is True


class TestAzureConfigurationIntegration:
    """Integration tests for Azure configuration with different scenarios."""

    def test_azure_config_with_different_environments(self, mock_blob_service_client):
        """Test Azure configuration works with different environment setups."""
        environments = [
            ('PROD', 'prod-logs', 'prod-score'),
            ('UAT', 'uat-logs', 'uat-score'),
            ('TEST', 'test-logs', 'test-score'),
            ('DEV', 'dev-logs', 'dev-score'),
        ]

        for env, container, prefix in environments:
            env_vars = {
                'AZURE_STORAGE_CONNECTION_STRING': 'DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net',
                'AZURE_LOG_CONTAINER_NAME': container,
                'AZURE_LOG_BLOB_PREFIX': prefix,
                'DEPLOYMENT_ENVIRONMENT': env
            }

            with patch.dict(os.environ, env_vars, clear=True):
                config = AzureLoggingConfig()

                assert config.environment == env
                assert config.container_name == container
                assert config.blob_prefix == prefix
                assert config.is_configured() is True

    def test_azure_sink_with_config_integration(self, mock_blob_service_client):
        """Test that AzureBlobStorageSink integrates properly with AzureLoggingConfig."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net',
            'AZURE_LOG_CONTAINER_NAME': 'integration-logs',
            'AZURE_LOG_BLOB_PREFIX': 'integration-test',
            'DEPLOYMENT_ENVIRONMENT': 'INTEGRATION'
        }

        with patch.dict(os.environ, env_vars, clear=True), \
             patch('threading.Thread'):

            config = AzureLoggingConfig()
            sink = AzureBlobStorageSink(
                connection_string=config.get_connection_string(),
                container_name=config.container_name,
                blob_prefix=config.blob_prefix,
                environment=config.environment
            )

            # Verify sink was configured with config values
            assert sink.connection_string == config.get_connection_string()
            assert sink.container_name == config.container_name
            assert sink.blob_prefix == config.blob_prefix
            assert sink.environment == config.environment
            assert f'{config.blob_prefix}_{config.environment}_' in sink.current_blob_name


class TestErrorHandlingIntegration:
    """Integration tests for error handling across the logging system."""

    @patch('autolodge.log_utils.utils.logger')
    def test_azure_dependencies_check_integration(self, mock_logger):
        """Test integration of Azure dependencies check with setup_logging."""
        # Test when dependencies are missing
        with patch('autolodge.log_utils.config.BlobServiceClient', side_effect=ImportError), \
             patch('autolodge.log_utils.utils._setup_local_file_logging', return_value='/logs/local.log') as mock_local:

            result = setup_logging()

            # Should fall back to local logging
            mock_local.assert_called_once()
            assert result == '/logs/local.log'

    @patch('autolodge.log_utils.utils.logger')
    def test_configuration_validation_integration(self, mock_logger, clean_environment):
        """Test configuration validation integration."""
        # Test with invalid/incomplete configuration
        with patch.dict(os.environ, {
            'AZURE_STORAGE_ACCOUNT_NAME': 'testaccount'
            # Missing AZURE_STORAGE_ACCOUNT_KEY
        }, clear=True), \
             patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=True), \
             patch('autolodge.log_utils.utils._setup_local_file_logging', return_value='/logs/local.log') as mock_local:

            result = setup_logging()

            # Should fall back to local logging due to incomplete configuration
            mock_local.assert_called_once()
            assert result == '/logs/local.log'

    def test_sink_error_handling_integration(self, mock_blob_service_client, mock_loguru_message):
        """Test error handling integration in the sink."""
        env_vars = {
            'AZURE_STORAGE_CONNECTION_STRING': 'DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net',
            'AZURE_LOG_CONTAINER_NAME': 'error-test-logs',
            'AZURE_LOG_BLOB_PREFIX': 'error-test',
            'DEPLOYMENT_ENVIRONMENT': 'ERROR_TEST'
        }

        with patch.dict(os.environ, env_vars, clear=True), \
             patch('threading.Thread'):

            # Create sink
            config = AzureLoggingConfig()
            sink = AzureBlobStorageSink(
                connection_string=config.get_connection_string(),
                container_name=config.container_name,
                blob_prefix=config.blob_prefix,
                environment=config.environment
            )

            # Test error handling in write method
            bad_message = Mock()
            bad_message.record = None  # This will cause an error

            # Should not raise exception, should handle gracefully
            sink.write(bad_message)

            # Test error handling in upload
            mock_blob_client = mock_blob_service_client.get_blob_client.return_value
            mock_blob_client.upload_blob.side_effect = Exception('Upload failed')

            sink.log_buffer = ['test message']
            # Should not raise exception, should handle gracefully
            sink._upload_buffer()


class TestEndToEndLoggingWorkflow:
    """End-to-end tests for the complete logging workflow."""

    @patch('autolodge.log_utils.utils.logger')
    def test_complete_azure_logging_workflow(self, mock_logger, azure_config_env_vars, mock_blob_service_client, mock_loguru_message):
        """Test complete end-to-end Azure logging workflow."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=True), \
             patch('threading.Thread'):

            # Step 1: Setup logging
            log_destination = setup_logging()

            # Verify Azure logging was configured
            assert 'azure://' in log_destination
            mock_logger.info.assert_any_call('✅ Azure Blob Storage logging configured successfully')

            # Step 2: Verify configuration was loaded correctly
            config = AzureLoggingConfig()
            assert config.is_configured() is True
            assert config.environment == 'TEST'

            # Step 3: Create sink manually to test integration
            sink = AzureBlobStorageSink(
                connection_string=config.get_connection_string(),
                container_name=config.container_name,
                blob_prefix=config.blob_prefix,
                environment=config.environment
            )

            # Step 4: Test message writing and uploading
            sink.write(mock_loguru_message)
            assert len(sink.log_buffer) == 1

            sink._upload_buffer()
            assert len(sink.log_buffer) == 0  # Buffer should be cleared after upload

            # Verify blob operations were called
            mock_blob_service_client.get_blob_client.assert_called()
            mock_blob_client = mock_blob_service_client.get_blob_client.return_value
            mock_blob_client.upload_blob.assert_called()

    def test_fallback_logging_workflow(self, clean_environment):
        """Test complete fallback logging workflow when Azure is not available."""
        with patch('autolodge.log_utils.utils.check_azure_dependencies', return_value=False), \
             patch('autolodge.log_utils.utils._setup_local_file_logging') as mock_local_setup:

            mock_local_setup.return_value = '/logs/fallback_LOCAL_2024-01-15.log'

            # Setup logging should fall back to local
            log_destination = setup_logging()

            # Verify local logging was configured
            mock_local_setup.assert_called_once()
            assert log_destination == '/logs/fallback_LOCAL_2024-01-15.log'

            # Verify configuration reflects no Azure setup
            config = AzureLoggingConfig()
            assert config.is_configured() is False
            assert config.environment == 'LOCAL'
